<?php
class NCS_Affiliate_Helper {

    public function __construct() { 
        add_action( 'init', [ $this, 'init' ], 10 );
    }

    public function init() {
        add_action( 'sc_run_after_integrations', array( $this, 'do_affiliate_commission' ), 10,3);
        add_action( 'sc_affiliate_new_conversion', array( $this, 'send_new_conversion_email') );
        add_action( 'sc_affiliate_commission_paid', array( $this, 'send_commission_paid_email') );
        add_action( 'sc_affiliate_created', array( $this, 'maybe_send_welcome_email') );
        add_action( 'sc_affiliate_updated', array( $this, 'maybe_send_welcome_email'), 10, 2 );
        add_action( 'sc_affiliate_application_submitted', array( $this, 'send_new_application_email') );
        add_filter( 'sc_notification_email_to', array( $this, 'new_application_to_address'), 10, 2);
    }

    public function new_application_to_address ($email, $type) {
        if ($type == 'affiliate_manager_application') {
            return get_option('_sc_affiliate_manager_email');
        }
        return $email;
    }

    function send_new_application_email($data) {

        if(!isset($data['id']) || !$data['id']) {
            return;
        } 

        $affiliate_id = $data['id'];
        $application_date = date_i18n(get_option('date_format'), strtotime($data['created_at']));

        $fields = [
            'first_name' => __('First Name', 'ncs-cart'),
            'last_name' => __('Last Name', 'ncs-cart'),
            'email' => __('Email', 'ncs-cart'),
            'paypal_email' => __('PayPal Email', 'ncs-cart'),
            'personalized_identifier' => __('Personalized Identifier', 'ncs-cart'),
            'created_at' => __('Application Date', 'ncs-cart')
        ];

        $all_affiliate_fields = [];

        foreach ($fields as $key => $label) {
            $value = $key === 'created_at' ? $application_date : ($data[$key] ?? '');
            if ($value) {
                $all_affiliate_fields[] = "$label: $value";
            }
        }

        // Join the fields with newlines
        $data['all_affiliate_fields'] = implode("\n", $all_affiliate_fields);

        $review_link = add_query_arg(
                [
                    'page' => 'sc_affiliate_dashboard_callback',
                    'tab' => 'edit_sc_affiliate',
                    'affiliate_id' => $affiliate_id,
                ],
                admin_url('admin.php')
            );
        
        $data['review_affiliate_link'] = '<a href="' . esc_url($review_link) . '" target="_blank">' . __('Review Affiliate Application', 'ncs-cart') . '</a>';

        studiocart_notification_send('affiliate_manager_application', $data);
    }

    function maybe_send_welcome_email($data, $old_data=false) {
        if( $data['status'] == 'active' && (!$old_data || $data['status'] != $old_data['status']) ) {
            studiocart_notification_send('affiliate_welcome', $data);
        }
    }

    public function do_affiliate_commission($status, $order, $event_type){
        global $wpdb, $sc_debug_logger;

        $sc_debug_logger->log_debug("in affiliate_commission ". print_r($order, true));

        $order = new ScrtOrder($order['ID']);

        $orderID         = $order->id;
        $productID       = $order->product_id;
        $aff_id          = $_COOKIE['sc_affiliate_id'] ?? null;
        $subscriptionID  = $order->subscription_id ?: '';
        $orderType       = $order->order_type ?: 'main';
        $table_name      = $wpdb->prefix . 'ncs_affiliate_commissions';
        $order->is_renewal = '';    
        
        $affUserID = get_post_meta( $orderID, '_sc_affiliate_id', true);
        $affiliate_ID = '';

        if(empty($affUserID )){
            if(!$aff_id) { $sc_debug_logger->log_debug("_sc_affiliate_id empty : ". print_r($aff_id, true)); return; }
            $affiliate_ID = $aff_id;
            update_post_meta( $orderID, '_sc_affiliate_id', $aff_id);
            update_post_meta( $subscriptionID, '_sc_affiliate_id', $aff_id);
        } else {
            $affiliate_ID = $affUserID;
        }

        if (in_array($event_type, ['subscription']) || in_array($status, ['pending-payment', 'pending', 'completed', 'failed', 'refunded', 'uncollectible'])) {
            return;
        }
        
        // check subscription for affiliate ID for renewals
        if($event_type == 'renewal') {
            $order->is_renewal = 1;
            $aff_id = get_post_meta( $subscriptionID, '_sc_affiliate_id', true);

            // if sub is from an upsell, change order type and set parent
            if ($order->order_parent = get_post_meta($subscriptionID, '_sc_us_parent', true)) {
                $orderType = 'upsell';
            } else if ($order->order_parent = get_post_meta($subscriptionID, '_sc_ds_parent', true)) {
                $orderType = 'downsell';
            }
        }

        // If this order has a commission recorded already just update its order status
        $results = $wpdb->get_results( $wpdb->prepare( "SELECT * FROM $table_name WHERE order_id = %d", $orderID ), ARRAY_A);

        $sc_debug_logger->log_debug("recorded already? ". print_r($results, true));

        if($results) {
            foreach ($results as $row) {

                $updates = array('order_status' => $order->status);
                $formats = array('%s');

                // if the order is refunded and commission is pending, update commission status
                if ($order->status === 'refunded' && $row['commission_status'] === 'pending') {
                    $updates['commission_status'] = 'refunded';
                    $formats[] = '%s';
                }

                // if the order status is paid, update the date_order_paid column
                if ($order->status === 'paid' && empty($row['date_order_paid'])) {
                    $updates['date_order_paid'] = current_time('mysql');
                    $formats[] = '%s';
                    do_action('sc_affiliate_new_conversion', $row);
                }

                $wpdb->update(
                    $table_name,
                    $updates,
                    array('order_id' => $orderID),
                    $formats,
                    array('%d')
                );

                if($orderType == 'main' && $event_type != 'renewal') {
                    $this->add_order_id_to_page_visit($row['affiliate_id'], $order->id, $order);
                }

            }
            return;
        }
        
        $affUserID = get_post_meta( $orderID, '_sc_affiliate_id', true);
        $affiliate_ID = '';
        
        if(empty($affUserID )){
            if(!$aff_id) { return; }
            $affiliate_ID = $aff_id;
            update_post_meta( $orderID, '_sc_affiliate_id', $aff_id);
            update_post_meta( $subscriptionID, '_sc_affiliate_id', $aff_id);
        } else {
            $affiliate_ID = $affUserID;
        }

        $sc_debug_logger->log_debug("aff_id: ". print_r($affiliate_ID, true));

        // process upsells/downsells for entire funnel types
        if($orderType != 'main'){

            $order_parent = new ScrtOrder($order->order_parent);

            $commission = $wpdb->get_var($wpdb->prepare(
                "SELECT commission_name FROM $table_name 
                WHERE order_id = %d AND commission_apply = %s",
                $order_parent->id, 'entire_funnel'
            ));

            if ($commission) {

                if (!$this->is_affiliation_active($order_parent->product_id, $affiliate_ID)) {
                    return;
                }
                
                if ($commissionMetas = get_post_meta($order_parent->product_id, '_sc_affiliate_commission_fields', true)) {
                    foreach ($commissionMetas as $cMeta) {

                        if($commission == $cMeta['affiliate_commission']){

                            if($cMeta['affiliate_type'] == 'amount') { return; }

                            $commission = [
                                'cname' => $cMeta['affiliate_commission'] ?? '',
                                'type' => $cMeta['affiliate_type'] ?? '',
                                'applyTo' => $cMeta['affiliate_apply_to'] ?? '',
                                'priceType' => $cMeta['affiliate_price'] ?? '', 
                                'amount' => $cMeta['affiliate_amt'] ?? '',
                                'percentage' => $cMeta['affiliate_percentage'] ?? '',
                                'renewal_limit' => $cMeta['renewal_limit'] ?? '',
                                'cprice' => ''
                            ]; 

                            $this->add_commission($affiliate_ID, $commission, $order, $order_parent->product_id, $orderType, $subscriptionID);
                            return;
                        }
                    }
                }
            }
        }

        if (!$this->is_affiliation_active($productID, $affiliate_ID)) {
            return;
        }

        $sc_debug_logger->log_debug("is_affiliation_active: ");

        
        // save commission to DB
        if ($commission = $this->get_commission($productID, $order)) {
            $this->add_commission($affiliate_ID, $commission, $order, $productID, $orderType, $subscriptionID);
        }
    }

    function send_new_conversion_email($commission) {
        if ($affiliate_details = $this->get_affiliate($commission['affiliate_id'])) {
            $commission = array_merge($commission, $affiliate_details);
            add_filter('sc_personalize_replacements', [$this, 'add_affiliate_email_tags'], 10, 2);
            studiocart_notification_send('new_conversion', $commission);
        }
    }

    function send_commission_paid_email($payout) {
        if ($affiliate_details = $this->get_affiliate($payout['affiliate_id'])) {
            $payout = array_merge($payout, $affiliate_details);
            add_filter('sc_personalize_replacements', [$this, 'add_affiliate_email_tags'], 10, 2);
            studiocart_notification_send('commission_paid', $payout);
        }
    }

    function add_affiliate_email_tags($replacements, $fields) {

        if ($login = get_option('_sc_affiliate_dashboard_page_id')) {
            $replacements['affiliate_login'] = get_permalink($login);
        }

        $replacements['affiliate_first_name'] = $fields['first_name'] ?? '';
        $replacements['affiliate_last_name'] = $fields['last_name'] ?? '';
        $replacements['affiliate_email'] = $fields['email'] ?? '';
        $replacements['affiliate_name'] = $replacements['affiliate_first_name'] . ' ' . $replacements['affiliate_last_name'];

        if (isset($fields['commission_price'])) {
            $replacements['affiliate_award'] = sc_format_price($fields['commission_price'], false);
        }

        if (isset($fields['payout_amount'])) {
            $replacements['payout_amount'] = sc_format_price($fields['payout_amount'], false);
        }

        return $replacements;
    }

    function get_affiliate($affiliate_id) {
        global $wpdb;
        
        // If it's an external affiliate, retrieve the details from the custom affiliate table
        $affiliate_table = $wpdb->prefix . 'ncs_affiliates';
        return $wpdb->get_row($wpdb->prepare(
            "SELECT * FROM $affiliate_table WHERE id = %d",
            $affiliate_id
        ), ARRAY_A);
        
    }

    function is_affiliation_active($productID, $affiliate_ID) {

        $affiliateActive = is_sc_aff_product($productID);

        $affiliate = new SC_Affiliate($affiliate_ID);
        $user_id = $affiliate->user_id;

        if(!$affiliateActive || !get_user_by('id', $user_id))  {
            return false;
        }

        // make sure this affiliate has been added to this product
        if($affiliate->status != 'active') {
            return false;
        }

        // Check if this affiliate is associated with this product
        $affiliate_products = $affiliate->get_affiliate_products(); // Returns products assigned to the affiliate
        $affiliate_product_ids = wp_list_pluck($affiliate_products, 'ID'); // Get an array of product IDs

        // If the product ID is in the affiliate's assigned products, they are active for this product
        if (in_array($productID, $affiliate_product_ids)) {
            return true;
        }

        return false;
    }

    public function get_commission($productID, $order) {
        $commission = array();

        if ($commissionMetas = get_post_meta($productID, '_sc_affiliate_commission_fields', true)) {
            foreach ($commissionMetas as $cMeta) {
                $priceType = $cMeta['affiliate_price'] ?? '';
                if( empty( $priceType ) || $priceType = '' || ((!empty($priceType)) && in_array($order->plan->option_id, $priceType)) ){
                    $commission = [
                        'cname' => $cMeta['affiliate_commission'] ?? '',
                        'type' => $cMeta['affiliate_type'] ?? '',
                        'applyTo' => $cMeta['affiliate_apply_to'] ?? '',
                        'priceType' => $cMeta['affiliate_price'] ?? '', 
                        'amount' => $cMeta['affiliate_amt'] ?? '',
                        'percentage' => $cMeta['affiliate_percentage'] ?? '',
                        'renewal_limit' => $cMeta['renewal_limit'] ?? '',
                        'cprice' => ''
                    ];                    
                    break;
                }
            }
        }
        return $commission;
    }
    public function add_commission($affiliate_ID, $commission, $order, $productID, $orderType, $subscriptionID = '') {

        global $wpdb;

        if(!$this->renewal_check($order, $commission)) {
            return;
        }

        $table_name = $wpdb->prefix . 'ncs_affiliate_commissions';
    
        $affiliate = new SC_Affiliate($affiliate_ID);
        $affiliate_user_id = $affiliate->user_id;

        $cm_cal = $this->commission_calculation( $order, $commission);
        $commission['cprice'] = $cm_cal['cprice'];
        $commission['total'] = $cm_cal['total'];
        $commission['rate'] = $cm_cal['rate'];

        $orderType = ($order->is_renewal) ? 'renewal' : $orderType;
    
        if ( !empty($commission['cprice']) ) {
            // Prepare data to insert into the database
            $data = [
                'affiliate_id' => $affiliate_ID,
                'affiliate_name' => get_the_author_meta('display_name', $affiliate_user_id),
                'commission_name' => $commission['cname'],
                'commission_type' => $commission['type'],
                'commission_apply' => $commission['applyTo'],
                'rate' => $commission['rate'],
                'order_id' => $order->id,
                'parent_order_id' => $order->order_parent,
                'product_id' => $productID,
                'product_name' => $order->product_name,
                'subscription_id' => $subscriptionID,
                'commission_price' => $commission['cprice'],
                'commission_status' => 'pending',
                'order_status' => $order->status,
                'total' => $commission['total'],
                'order_type' => $orderType,
                'date_recorded' => current_time('mysql'),
            ];

            // Check if the order is paid and set the date_paid column
            if ($order->status === 'paid') {
                $data['date_order_paid'] = current_time('mysql');  // Set the date_paid if the order is paid
                do_action('sc_affiliate_new_conversion', $data);
            }

            // Insert into the database
            $wpdb->insert($table_name, $data);

            // Link page visit to order
            if($orderType == 'main') {
                $this->add_order_id_to_page_visit($affiliate_ID, $order->id, $order);
            }

        }
    }

    public function add_order_id_to_page_visit($affiliate_id, $order_id, $order) {
        global $wpdb, $sc_debug_logger;
    
        $visit_table_name = $wpdb->prefix . 'ncs_affiliate_visits';
        $ip_address = $order->ip_address; // Retrieve the user's IP address
    
        // Find the latest visit for this affiliate on the page URL and update the order ID
        $existing_visit = $wpdb->get_row($wpdb->prepare(
            "SELECT * FROM $visit_table_name 
            WHERE affiliate_id = %d 
            AND page_url = %s 
            AND ip_address = %s 
            ORDER BY date_recorded DESC 
            LIMIT 1",
            $affiliate_id,
            get_permalink($order->page_id),
            $ip_address
        ));

        $sc_debug_logger->log_debug("find latest visit to update order id ". print_r($wpdb->last_query, true));

        if ($existing_visit) {
            // Update the visit to add the order ID if the visit exists
            $wpdb->update(
                $visit_table_name,
                ['order_id' => $order_id],
                ['id' => $existing_visit->id],
                ['%d'],
                ['%d']
            );
        }
    }

    public function renewal_check($order, $commission) {
        // check limits on renewals
        if( isset($order->renewal_order) && $order->renewal_order && $commission['renewal_limit'] ){
            $orderDate   = new DateTime(get_post_field('post_date', $order->id));
            $startDate = new DateTime(get_post_field('post_date', $order->subscription_id));
            $interval    = $startDate->diff($orderDate);
            $daysPassed  = $interval->days;

            if ($daysPassed > ($commission['renewal_limit'] ?? 0)) {
                return false;
            }
        }
        return true;
    }

    public function commission_calculation($order, $commission){
        $orderAmount = $order->amount;
        $commissionRate = $commission['rate'] = $commission['percentage'];
        $percentage = floatval($commissionRate) / 100;

        if ($commission['applyTo'] == 'product_only') {
            $orderAmount = $order->main_offer_amt;
        } 

        if ($commission['type'] == 'amount' && ( $commission['amount'] != '' || $commission['amount'] != 0 ) ) {
            $commission['cprice'] = $commission['rate'] = $commission['total'] = $commission['amount'];
        } else {
            $commission['cprice'] = $orderAmount * $percentage;
        }

        $commission['total'] = $orderAmount;

        return $commission;
    }

}

$sc_affiliate = new NCS_Affiliate_Helper();
